declare const _default: import('vue').DefineComponent<import('vue').ExtractPropTypes<{
    /** 当前排序字段 */
    sort: StringConstructor;
    /** 当前排序方式 */
    order: StringConstructor;
    /** 排序字段名称 */
    name: {
        type: StringConstructor;
        required: true;
    };
}>, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<import('vue').ExtractPropTypes<{
    /** 当前排序字段 */
    sort: StringConstructor;
    /** 当前排序方式 */
    order: StringConstructor;
    /** 排序字段名称 */
    name: {
        type: StringConstructor;
        required: true;
    };
}>> & Readonly<{}>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
export default _default;
