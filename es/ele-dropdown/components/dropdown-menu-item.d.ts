import { PropType } from 'vue';
import { ElIconProps } from '../../ele-app/el';
import { DropdownItem } from '../types';

declare function __VLS_template(): Partial<Record<string, (_: {
    item: DropdownItem;
}) => any>> & {
    subMenus?(_: {}): any;
};
declare const __VLS_component: import('vue').DefineComponent<import('vue').ExtractPropTypes<{
    /** 菜单项数据 */
    item: {
        type: PropType<DropdownItem>;
        required: true;
    };
    /** 选中的菜单 */
    selected: PropType<DropdownItem["command"]>;
    /** 自定义图标属性 */
    iconProps: PropType<ElIconProps>;
}>, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {
    itemClick: (_item: DropdownItem) => void;
    wrapperContext: (_e: MouseEvent) => void;
}, string, import('vue').PublicProps, Readonly<import('vue').ExtractPropTypes<{
    /** 菜单项数据 */
    item: {
        type: PropType<DropdownItem>;
        required: true;
    };
    /** 选中的菜单 */
    selected: PropType<DropdownItem["command"]>;
    /** 自定义图标属性 */
    iconProps: PropType<ElIconProps>;
}>> & Readonly<{
    onItemClick?: ((_item: DropdownItem) => any) | undefined;
    onWrapperContext?: ((_e: MouseEvent) => any) | undefined;
}>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, ReturnType<typeof __VLS_template>>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
