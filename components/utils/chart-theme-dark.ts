/** echarts 暗黑主题 */
export const ChartThemeDark = {
  color: [
    '#5b8ff9',
    '#61ddaa',
    '#65789b',
    '#f6bd16',
    '#7262fd',
    '#78d3f8',
    '#9661bc',
    '#f6903d',
    '#008685',
    '#f08bb4'
  ],
  backgroundColor: 'rgba(0,0,0,0)',
  textStyle: {},
  title: {
    textStyle: {
      color: '#f2f2f2'
    },
    subtextStyle: {
      color: '#bfbfbf'
    }
  },
  line: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: '2'
    },
    symbolSize: 4,
    symbol: 'emptyCircle',
    smooth: false
  },
  radar: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: '2'
    },
    symbolSize: 4,
    symbol: 'emptyCircle',
    smooth: false
  },
  bar: {
    barCategoryGap: '50%',
    itemStyle: {
      barBorderWidth: '0',
      barBorderColor: '#000'
    }
  },
  pie: {
    itemStyle: {
      borderWidth: '2',
      borderColor: '#000'
    }
  },
  scatter: {
    itemStyle: {
      borderWidth: '0',
      borderColor: '#000'
    }
  },
  boxplot: {
    itemStyle: {
      borderWidth: '0',
      borderColor: '#000'
    }
  },
  parallel: {
    itemStyle: {
      borderWidth: '0',
      borderColor: '#000'
    }
  },
  sankey: {
    itemStyle: {
      borderWidth: '0',
      borderColor: '#000'
    }
  },
  funnel: {
    itemStyle: {
      borderWidth: '0',
      borderColor: '#000'
    }
  },
  gauge: {
    itemStyle: {
      borderWidth: '0',
      borderColor: '#000'
    }
  },
  candlestick: {
    itemStyle: {
      color: '#fd1050',
      color0: '#0cf49b',
      borderColor: '#fd1050',
      borderColor0: '#0cf49b',
      borderWidth: 1
    }
  },
  graph: {
    itemStyle: {
      borderWidth: '0',
      borderColor: '#000'
    },
    lineStyle: {
      width: 1,
      color: '#434343'
    },
    symbolSize: 4,
    symbol: 'emptyCircle',
    smooth: false,
    color: [
      '#5b8ff9',
      '#61ddaa',
      '#65789b',
      '#f6bd16',
      '#7262fd',
      '#78d3f8',
      '#9661bc',
      '#f6903d',
      '#008685',
      '#f08bb4'
    ],
    label: {
      color: '#bfbfbf'
    }
  },
  map: {
    itemStyle: {
      areaColor: '#202020',
      borderColor: '#303030',
      borderWidth: 1
    },
    label: {
      color: '#f2f2f2',
      textBorderColor: '#000',
      textBorderWidth: 1
    },
    emphasis: {
      itemStyle: {
        areaColor: 'rgba(255,215,0,0.8)',
        borderColor: '#303030',
        borderWidth: 1
      },
      label: {
        color: '#f2f2f2'
      }
    }
  },
  geo: {
    itemStyle: {
      areaColor: '#202020',
      borderColor: '#303030',
      borderWidth: 1
    },
    label: {
      color: '#f2f2f2',
      textBorderColor: '#000',
      textBorderWidth: 1
    },
    emphasis: {
      itemStyle: {
        areaColor: 'rgba(255,215,0,0.8)',
        borderColor: '#303030',
        borderWidth: 1
      },
      label: {
        color: '#f2f2f2'
      }
    }
  },
  grid: {
    top: 30,
    right: 20,
    left: 60,
    bottom: 40
  },
  categoryAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: '#434343'
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: '#434343'
      },
      alignWithLabel: true
    },
    axisLabel: {
      show: true,
      color: '#bfbfbf'
    },
    splitLine: {
      show: false,
      lineStyle: {
        type: 'dashed',
        color: ['#303030']
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ['#202020']
      }
    }
  },
  valueAxis: {
    axisLine: {
      show: false,
      lineStyle: {
        color: '#434343'
      }
    },
    axisTick: {
      show: false,
      lineStyle: {
        color: '#434343'
      }
    },
    axisLabel: {
      show: true,
      color: '#bfbfbf'
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: ['#303030']
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ['#202020']
      }
    }
  },
  logAxis: {
    axisLine: {
      show: false,
      lineStyle: {
        color: '#434343'
      }
    },
    axisTick: {
      show: false,
      lineStyle: {
        color: '#434343'
      }
    },
    axisLabel: {
      show: true,
      color: '#bfbfbf'
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: ['#303030']
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ['#202020']
      }
    }
  },
  timeAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: '#434343'
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: '#434343'
      }
    },
    axisLabel: {
      show: true,
      color: '#bfbfbf'
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ['#303030']
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ['#202020']
      }
    }
  },
  toolbox: {
    iconStyle: {
      borderColor: '#8c8c8c'
    },
    emphasis: {
      iconStyle: {
        borderColor: '#bfbfbf'
      }
    }
  },
  legend: {
    inactiveColor: '#595959',
    inactiveBorderColor: '#000',
    textStyle: {
      color: '#bfbfbf',
      lineHeight: 14
    }
  },
  tooltip: {
    axisPointer: {
      lineStyle: {
        color: '#434343',
        width: '1'
      },
      crossStyle: {
        color: '#434343',
        width: '1'
      }
    },
    backgroundColor: 'rgba(48,48,48,0.95)',
    borderWidth: 0,
    textStyle: {
      color: '#ffffff'
    }
  },
  timeline: {
    lineStyle: {
      color: '#303030',
      width: '1'
    },
    itemStyle: {
      color: '#303030',
      borderWidth: '1'
    },
    controlStyle: {
      color: '#303030',
      borderColor: '#303030',
      borderWidth: '0.5'
    },
    checkpointStyle: {
      color: '#5b8ff9',
      borderColor: '#5b8ff9'
    },
    label: {
      color: '#8c8c8c'
    },
    emphasis: {
      itemStyle: {
        color: '#5b8ff9'
      },
      controlStyle: {
        color: '#303030',
        borderColor: '#303030',
        borderWidth: '0.5'
      },
      label: {
        color: '#bfbfbf'
      }
    }
  },
  visualMap: {
    itemHeight: 80,
    itemWidth: 15,
    color: ['#5b8ff9', '#294070', '#111c35'],
    textStyle: {
      color: '#bfbfbf'
    }
  },
  dataZoom: {
    handleSize: '100%',
    textStyle: {
      color: '#bfbfbf'
    }
  },
  markPoint: {
    label: {
      color: '#ffffff'
    }
  }
};
