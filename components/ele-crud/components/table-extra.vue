<!-- 列表额外功能 -->
<template>
  <EditModal
    v-if="addConfig !== false"
    :data="addData"
    :modelValue="addVisible"
    :title="lang.add"
    :editConfig="addConfig === true ? {} : addConfig"
    :fields="fields"
    :getFieldsEditFormItems="getFieldsAddFormItems"
    :getAndCacheCode="getAndCacheCode"
    :proFormComponent="proFormComponent"
    :editApi="addApi"
    :itemTypeData="itemTypeData"
    :httpRequest="httpRequest"
    :screenSize="screenSize"
    :lang="lang"
    @editError="handleAddError"
    @editDone="handleAddDone"
    @update:modelValue="handleUpdateAddVisible"
  >
    <template
      v-for="name in Object.keys($slots).filter(
        (k) => !slotExcludes.includes(k)
      )"
      #[name]="slotProps"
    >
      <slot :name="name" v-bind="slotProps || void 0"></slot>
    </template>
  </EditModal>
  <EditModal
    v-if="editConfig !== false"
    :data="editData"
    :modelValue="editVisible"
    :title="lang.edit"
    :editConfig="editConfig === true ? {} : editConfig"
    :fields="fields"
    :getFieldsEditFormItems="getFieldsEditFormItems"
    :getAndCacheCode="getAndCacheCode"
    :proFormComponent="proFormComponent"
    :editApi="editApi"
    :itemTypeData="itemTypeData"
    :httpRequest="httpRequest"
    :screenSize="screenSize"
    :lang="lang"
    @editError="handleEditError"
    @editDone="handleEditDone"
    @update:modelValue="handleUpdateEditVisible"
  >
    <template
      v-for="name in Object.keys($slots).filter(
        (k) => !slotExcludes.includes(k)
      )"
      #[name]="slotProps"
    >
      <slot :name="name" v-bind="slotProps || {}"></slot>
    </template>
  </EditModal>
  <ElePopconfirm
    v-if="delPopConfirmProps"
    ref="delConfirmRef"
    :width="200"
    :triggerKeys="[]"
    :persistent="false"
    :popperOptions="{
      strategy: 'fixed',
      modifiers: [{ name: 'offset', options: { offset: [12, 6] } }]
    }"
    :virtualTriggering="true"
    :virtualRef="delConfirmVirtualRef"
    placement="top-end"
    :content="lang.deleteConfirm"
    v-bind="delPopConfirmProps"
    @confirm="handleConfirm"
  />
  <slot></slot>
</template>

<script lang="ts" setup>
  import type { PropType } from 'vue';
  import { ref, nextTick, unref, watch } from 'vue';
  import type { UserComponent } from '../../ele-app/types';
  import type {
    ElePopconfirmInstance,
    ElePopconfirmProps
  } from '../../ele-app/plus';
  import { omit } from '../../utils/common';
  import type { DataItem } from '../../ele-data-table/types';
  import ElePopconfirm from '../../ele-popconfirm/index.vue';
  import type {
    ProFormItemTypeData,
    ScreenSize
  } from '../../ele-pro-form/types';
  import { getFieldsAddFormItems, getFieldsEditFormItems } from '../util';
  import type {
    EditConfig,
    BtnClickAction,
    CrudField,
    EditApi,
    DeletePopOption,
    GetAndCacheCodeFunction,
    CrudLocale
  } from '../types';
  import EditModal from './edit-modal.vue';
  const slotExcludes = ['default'];

  defineOptions({ name: 'TableExtra' });

  const props = defineProps({
    /** 添加弹窗是否打开 */
    addVisible: Boolean as PropType<boolean>,
    /** 添加弹窗数据 */
    addData: Object as PropType<DataItem>,
    /** 修改弹窗是否打开 */
    editVisible: Boolean as PropType<boolean>,
    /** 修改弹窗数据 */
    editData: Object as PropType<DataItem>,
    /** 删除气泡配置 */
    deletePopOption: Object as PropType<DeletePopOption>,
    /** 添加配置 */
    addConfig: [Object, Boolean] as PropType<EditConfig | boolean>,
    /** 修改配置 */
    editConfig: [Object, Boolean] as PropType<EditConfig | boolean>,
    /** 字段数据 */
    fields: Array as PropType<CrudField[]>,
    /** 获取字段数据对应的表单项的方法 */
    getAndCacheCode: {
      type: Function as PropType<GetAndCacheCodeFunction>,
      required: true
    },
    /** 高级表单组件 */
    proFormComponent: [String, Object, Function] as PropType<UserComponent>,
    /** 添加数据接口 */
    addApi: [Function, String] as PropType<EditApi | string>,
    /** 修改数据接口 */
    editApi: [Function, String] as PropType<EditApi | string>,
    /** 高级表单组件类型数据 */
    itemTypeData: Array as PropType<ProFormItemTypeData[]>,
    /** 远程数据源请求工具 */
    httpRequest: [Object, Function],
    /** 屏幕尺寸 */
    screenSize: String as PropType<ScreenSize>,
    /** 国际化 */
    lang: {
      type: Object as PropType<Partial<CrudLocale>>,
      required: true
    }
  });

  const emit = defineEmits({
    /** 更新添加弹窗打开状态事件 */
    'update:addVisible': (_visible?: boolean) => true,
    /** 更新修改弹窗打开状态事件 */
    'update:editVisible': (_visible?: boolean) => true,
    /** 操作按钮点击事件 */
    btnClick: (_action: BtnClickAction, _e: MouseEvent, _item?: DataItem) =>
      true,
    /** 添加保存失败事件 */
    addError: (_e: Error) => true,
    /** 添加保存成功事件 */
    addDone: (_msg?: string) => true,
    /** 修改保存失败事件 */
    editError: (_e: Error) => true,
    /** 修改保存成功事件 */
    editDone: (_msg?: string) => true
  });

  /** 删除确认组件 */
  const delConfirmRef = ref<ElePopconfirmInstance>(null);

  /** 删除确认虚拟触发节点 */
  const delConfirmVirtualRef = ref<any>();

  /** 删除确认自定义属性 */
  const delPopConfirmProps = ref<ElePopconfirmProps>();

  /** 删除确认对应的数据 */
  let delConfirmCurrentData: DataItem | undefined;

  /** 删除确认事件 */
  const handleConfirm = (e: MouseEvent) => {
    emit('btnClick', 'delConfirm', e, delConfirmCurrentData);
  };

  /** 添加保存失败事件 */
  const handleAddError = (e: Error) => {
    emit('addError', e);
  };

  /** 添加保存成功事件 */
  const handleAddDone = (msg?: string) => {
    emit('addDone', msg);
  };

  /** 修改保存失败事件 */
  const handleEditError = (e: Error) => {
    emit('editError', e);
  };

  /** 修改保存成功事件 */
  const handleEditDone = (msg?: string) => {
    emit('editDone', msg);
  };

  /** 更新添加弹窗打开状态 */
  const handleUpdateAddVisible = (visible?: boolean) => {
    emit('update:addVisible', visible);
  };

  /** 更新修改弹窗打开状态 */
  const handleUpdateEditVisible = (visible?: boolean) => {
    emit('update:editVisible', visible);
  };

  /** 打开删除确认 */
  const openDelConfirm = (
    triggerEl: any,
    item?: DataItem,
    confirmProps?: ElePopconfirmProps
  ) => {
    if (triggerEl == null || delConfirmVirtualRef.value === triggerEl) {
      return;
    }
    delConfirmRef.value && delConfirmRef.value.hidePopper();
    nextTick(() => {
      delPopConfirmProps.value = confirmProps || {};
      delConfirmVirtualRef.value = triggerEl;
      delConfirmCurrentData = item;
      nextTick(() => {
        if (delConfirmRef.value) {
          unref(delConfirmRef.value.tooltipRef)?.handleOpen?.();
        }
      });
    });
  };

  watch(
    () => props.deletePopOption,
    (option) => {
      if (option) {
        openDelConfirm(
          option.triggerEl,
          option.item,
          omit(option.confirmProps, ['isPopConfirm']) as ElePopconfirmProps
        );
      }
    }
  );
</script>
