@use '../../style/themes/default.scss' as *;
@use '../../style/util.scss' as *;
@use './css-var.scss' as *;

@include set-modal-var($ele);

.ele-modal {
  &.el-overlay {
    height: auto;
  }

  & > .el-overlay-dialog {
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    & > .el-dialog {
      padding: 0;
      flex-shrink: 0;
      position: relative;
      background: eleVar('modal', 'bg');
      border-radius: eleVar('modal', 'radius');
      margin-bottom: eleVar('modal', 'mobile-space');

      & > .el-dialog__header,
      & > .el-dialog__footer {
        flex-shrink: 0;
        border: none;
        padding: 0;
        margin: 0;
      }

      & > .el-dialog__body {
        padding: 0;
        color: inherit;
        font-size: inherit;
      }
    }
  }
}

/* header */
.ele-modal-header {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: eleVar('modal', 'header-padding');
  border-bottom: eleVar('modal', 'header-border');
}

.ele-modal-title {
  flex: 1;
  color: eleVar('modal', 'header-color');
  font-size: eleVar('modal', 'header-font-size');
  line-height: eleVar('modal', 'header-line-height');
  font-weight: eleVar('modal', 'header-font-weight');
  box-sizing: border-box;
}

/* 图标按钮 */
.ele-modal-tool {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: eleVar('modal', 'icon-size');
  height: eleVar('modal', 'icon-size');
  line-height: eleVar('modal', 'icon-size');
  color: eleVar('modal', 'icon-color');
  font-size: eleVar('modal', 'icon-font-size');
  border-radius: eleVar('modal', 'icon-radius');
  transition: (color $transition-base, background-color $transition-base);
  box-sizing: border-box;
  cursor: pointer;

  &:hover {
    color: eleVar('modal', 'icon-hover-color');
    background: eleVar('modal', 'icon-hover-bg');
  }

  & + .ele-modal-tool {
    margin-left: eleVar('modal', 'icon-space');
  }
}

/* body */
.ele-modal-body {
  padding: eleVar('modal', 'body-padding');
  box-sizing: border-box;

  &.is-form {
    padding: eleVar('modal', 'form-body-padding');
  }
}

/* footer */
.ele-modal-footer {
  padding: eleVar('modal', 'footer-padding');
  border-top: eleVar('modal', 'footer-border');
  box-sizing: border-box;
}

/* 默认位置 */
.ele-modal-top > .el-overlay-dialog > .el-dialog {
  margin: 0;
}

.ele-modal-center > .el-overlay-dialog > .el-dialog {
  margin: auto;
}

.ele-modal-bottom > .el-overlay-dialog > .el-dialog {
  margin: auto auto 0 auto;
}

.ele-modal-left > .el-overlay-dialog > .el-dialog {
  margin: auto auto auto 0;
}

.ele-modal-right > .el-overlay-dialog > .el-dialog {
  margin: auto 0 auto auto;
}

.ele-modal-left-top > .el-overlay-dialog > .el-dialog {
  margin: 0 auto 0 0;
}

.ele-modal-left-bottom > .el-overlay-dialog > .el-dialog {
  margin: auto auto 0 0;
}

.ele-modal-right-top > .el-overlay-dialog > .el-dialog {
  margin: 0 0 0 auto;
}

.ele-modal-right-bottom > .el-overlay-dialog > .el-dialog {
  margin: auto 0 0 auto;
}

/* 支持拖拽 */
.ele-modal-movable > .el-overlay-dialog > .el-dialog > .el-dialog__header {
  cursor: move;
  user-select: none;
}

/* 支持拉伸 */
.ele-modal-resize-icon {
  position: absolute;
  right: 2px;
  bottom: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: eleVar('modal', 'icon-color');
  font-size: 12px;
  cursor: se-resize;

  &.is-horizontal {
    cursor: e-resize;
  }

  &.is-vertical {
    cursor: s-resize;
  }
}

.ele-modal-resizable > .el-overlay-dialog > .el-dialog,
.ele-modal-fullscreen > .el-overlay-dialog > .el-dialog {
  display: flex;
  flex-direction: column;

  & > .el-dialog__body {
    flex: auto;
    overflow: auto;
  }
}

/* 全屏 */
.ele-modal-fullscreen > .el-overlay-dialog > .el-dialog {
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  max-width: none !important;
  min-width: auto !important;
  max-height: none !important;
  min-height: auto !important;
  border-radius: 0 !important;

  & > .el-dialog__header {
    cursor: default;

    & > .ele-modal-resize-icon {
      display: none;
    }
  }
}

/* 同时打开多个 */
.ele-modal-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  box-sizing: border-box;
  z-index: elVar('index', 'popper');
  overflow: hidden;
}

.ele-modal-multiple {
  pointer-events: none !important;
  position: absolute !important;

  & > .el-overlay-dialog {
    pointer-events: none;
    overflow: hidden;

    & > .el-dialog {
      pointer-events: auto;
    }
  }
}

/* 失活状态 */
.ele-modal-hide {
  display: none !important;
}

/* 最大宽度适应屏幕 */
.ele-modal-responsive > .el-overlay-dialog > .el-dialog {
  max-width: calc(100% - #{eleVar('modal', 'mobile-space')} * 2);
}

/* 限制在主体区域 */
.ele-admin-modals > .ele-modal {
  pointer-events: auto;
  position: absolute !important;
}
