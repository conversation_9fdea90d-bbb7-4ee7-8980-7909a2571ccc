@use '../../style/themes/default.scss' as *;
@use '../../style/util.scss' as *;

/* 标签 */
.ele-edit-tag {
  margin-top: 8px;

  .el-tag {
    margin: 0 8px 8px 0;
    vertical-align: top;
  }
}

/* 添加按钮 */
.ele-edit-tag-add {
  &.el-tag {
    cursor: text;
    margin-right: 0;
    border-style: dashed;
    background: none;
  }

  & > .el-tag__content {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.ele-edit-tag .ele-edit-tag-add-icon {
  width: 1em;
  height: 1em;
  border-radius: 0;
  font-size: inherit;
  cursor: inherit;
  opacity: 0.6;
}

/* 禁用 */
.ele-edit-tag.is-disabled .ele-edit-tag-add {
  cursor: not-allowed;
  color: elVar('disabled', 'text-color');
  background: elVar('disabled', 'bg-color');
  border-color: elVar('disabled', 'border-color');
}

/* 只读 */
.ele-edit-tag.is-readonly > .el-tag:last-child {
  margin-right: 0;
}

/* 输入框 */
span.el-tag.ele-edit-tag-input {
  padding: 0;
}

.ele-edit-tag-input {
  &.el-tag {
    width: 80px;
    margin-right: 0;
    display: inline-block;
    background: none;
    border: none;
  }

  & > .el-tag__content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: inherit;
    line-height: inherit;
    border-radius: inherit;

    & > .el-input {
      flex: 1;
      width: 100%;
      height: 100%;
      font-size: inherit;
      border-radius: inherit;
    }
  }

  .el-input {
    .el-input__wrapper {
      height: 100%;
      padding-left: 8px;
      padding-right: 8px;
      box-sizing: border-box;
      border-radius: inherit;
    }

    .el-input__inner {
      height: 100%;
      line-height: inherit;
    }
  }

  &.is-error .el-input .el-input__wrapper {
    box-shadow: 0 0 0 0.8px elVar('color-danger') inset;
  }
}

/* 小尺寸 */
.ele-edit-tag.is-small .ele-edit-tag-input {
  width: 60px;

  .el-input .el-input__wrapper {
    padding-left: 4px;
    padding-right: 4px;
  }
}

/* 大尺寸 */
.ele-edit-tag.is-large .ele-edit-tag-input {
  width: 100px;

  .el-input .el-input__wrapper {
    padding-left: 12px;
    padding-right: 12px;
  }
}
