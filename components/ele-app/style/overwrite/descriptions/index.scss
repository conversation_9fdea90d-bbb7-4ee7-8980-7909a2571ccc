@use '../../../../style/themes/default.scss' as *;
@use '../../../../style/util.scss' as *;
@use './css-var.scss' as *;

@include set-el-descriptions-var($ele);

/* Descriptions */
.el-descriptions {
  .el-descriptions__body {
    background: none;
  }

  .el-descriptions__table.is-bordered {
    border-spacing: 0;
    border-collapse: separate;
    border-radius: eleVar('descriptions', 'radius');
    border-top: 1px solid eleVar('descriptions', 'border-color');
    border-left: 1px solid eleVar('descriptions', 'border-color');

    tr {
      & > .el-descriptions__cell {
        border-top: none;
        border-left: none;
        border-color: eleVar('descriptions', 'border-color');
      }

      &:first-child > .el-descriptions__cell {
        &:first-child {
          border-top-left-radius: eleVar('descriptions', 'radius');
        }

        &:last-child {
          border-top-right-radius: eleVar('descriptions', 'radius');
        }
      }

      &:last-child > .el-descriptions__cell {
        &:first-child {
          border-bottom-left-radius: eleVar('descriptions', 'radius');
        }

        &:last-child {
          border-bottom-right-radius: eleVar('descriptions', 'radius');
        }
      }
    }
  }

  .el-descriptions__label.el-descriptions__cell.is-bordered-label {
    background: eleVar('descriptions', 'bg');
  }

  .el-descriptions__cell {
    word-break: break-all;
  }
}
