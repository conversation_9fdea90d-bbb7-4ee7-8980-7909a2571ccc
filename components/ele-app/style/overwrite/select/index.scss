@use '../../../../style/themes/default.scss' as *;
@use '../../../../style/util.scss' as *;
@use './css-var.scss' as *;

@include set-el-select-var($ele);

/* Select */
.el-select__popper {
  .el-select-dropdown__list {
    margin: 0 !important;
    padding: eleVar('select', 'padding') !important;
  }

  .el-select-dropdown__item {
    height: eleVar('select', 'item-height');
    line-height: eleVar('select', 'item-height');
    padding: eleVar('select', 'item-padding');
    border-radius: eleVar('select', 'item-radius');
    transition: (color $transition-base, background-color $transition-base);
    box-sizing: border-box;
    display: flex;
    align-items: center;

    & > span {
      flex: 1;
    }

    & + .el-select-dropdown__item {
      margin-top: eleVar('select', 'item-margin');
    }

    &.selected,
    &.is-selected {
      background: eleVar('select', 'item-active-bg');
      font-weight: eleVar('select', 'item-active-font-weight');

      &:hover,
      &.hover,
      &.is-hovering {
        background: eleVar('select', 'item-active-hover-bg');
      }
    }
  }

  .el-select-dropdown__list .el-select-dropdown__item > span {
    font-size: inherit;
  }

  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected,
  .el-select-dropdown.is-multiple .el-select-dropdown__item.is-selected {
    background: eleVar('select', 'item-active-bg');

    &:hover,
    &.hover,
    &.is-hovering {
      background: eleVar('select', 'item-active-hover-bg');
    }

    &::after {
      width: eleVar('select', 'icon-size');
      height: eleVar('select', 'icon-size');
      margin: eleVar('select', 'icon-margin');
      display: inline-block;
      position: static;
      top: auto;
      right: auto;
      transform: none;
    }
  }

  .el-select-group__title {
    padding: eleVar('select', 'item-padding');
  }

  .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0;

    &::after {
      display: none;
    }
  }
}

/* 多选标签 */
.el-select-tags-wrapper .el-tag__content,
.el-select__collapse-tag .el-tag__content,
.el-select__selected-item .el-tag__content {
  display: inline-flex;
  align-items: center;
}

.el-select .el-tag__content .el-select__tags-text {
  line-height: 1;
}

.el-select .el-select-tags-wrapper.has-prefix {
  margin: 0 !important;
  box-sizing: border-box;
}

.el-select .el-select__tags {
  box-sizing: border-box;
  padding: 0 0 0 4px;
  left: 0;
}

.el-select .el-select__selection.is-near {
  gap: 0;
  margin: 0 0 0 calc(0px - #{eleVar('input', 'padding-left', 12px)});
  padding: 1px 0 1px 3px;
  box-sizing: border-box;
}

.el-select .el-select__tags .el-tag,
.el-select__collapse-tags .el-select__collapse-tag .el-tag {
  flex-shrink: 0;
  margin: 2px 4px 2px 0;
  height: calc(elVar('component-size') - 4px - 4px);
  border: none;
}

.el-select .el-select__tags .el-select__input {
  margin: 0 0 0 5.8px !important;
}

.el-select__selection.is-near > .el-select__selected-item,
.el-popper > .el-select__selection > .el-select__selected-item {
  flex-shrink: 0;
  margin: 2px 4px 2px 0;
}

.el-select__selection .el-select__selected-item > .el-tag {
  height: calc(elVar('component-size') - 4px - 4px);
  border: none;
}

/* 小尺寸 */
.el-select--small .el-select__tags .el-tag--small,
.el-select__collapse-tags .el-select__collapse-tag .el-tag--small {
  margin: 1px 4px 1px 0;
  height: calc(elVar('component-size', 'small') - 4px - 2px);
}

.el-select--small .el-select__selection.is-near {
  margin: 0 0 0 calc(0px - #{eleVar('input-sm', 'padding-left', 8px)});
  padding: 1px 0 1px 3px;
}

.el-select--small .el-select__selection.is-near > .el-select__selected-item {
  margin: 1px 4px 1px 0;
}

.el-select--small .el-select__selected-item > .el-tag,
.el-popper > .el-select__selection .el-tag--small {
  height: calc(elVar('component-size', 'small') - 4px - 2px);
}

.el-select--small .el-select__tags .el-select__input {
  margin: 0 0 0 3.8px !important;
}

/* 大尺寸 */
.el-select--large .el-select__tags {
  padding: 1px 0 1px 6px;
}

.el-select--large .el-select__selection.is-near {
  padding: 3px 0 3px 5px;
}

.el-select--large .el-select__tags .el-tag,
.el-select--large .el-select__selected-item > .el-tag {
  height: calc(elVar('component-size', 'large') - 8px - 4px);
}

.el-select--large .el-select__selection.is-near {
  margin: 0 0 0 calc(0px - #{eleVar('input-lg', 'padding-left', 16px)});
}

.el-select--large .el-select__tags .el-select__input {
  margin: 0 0 0 7.8px !important;
}

/* 多选搜索框 */
.el-select-tags-wrapper.has-prefix + .el-select__input {
  margin: 0 0 0 2px !important;
}

/* 标签溢出气泡 */
.el-popper > .el-select__collapse-tags,
.el-popper > .el-select__selection {
  margin: -5px -11px;
  padding: 8px 10px;
  box-sizing: border-box;
  gap: 0;
}
