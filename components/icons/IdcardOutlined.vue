<template>
  <svg
    viewBox="0 0 48 48"
    fill="none"
    stroke="currentColor"
    stroke-width="4"
    stroke-linejoin="round"
  >
    <path d="M3 8H45V40H3Z" />
    <path
      d="M17 25C19.2 25 21 23.2 21 21 21 18.8 19.2 17 17 17 14.8 17 13 18.8 13 21 13 23.2 14.8 25 17 25M23 31C23 27.7 20.3 25 17 25 13.7 25 11 27.7 11 31"
      stroke-width="3"
    />
    <path d="M26 20H38" />
    <path d="M30 28H38" />
  </svg>
</template>

<script lang="ts" setup>
  defineOptions({ name: 'IdcardOutlined' });
</script>
