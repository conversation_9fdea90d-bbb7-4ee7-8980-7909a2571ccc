<template>
  <svg viewBox="0 0 48 48" fill="currentColor">
    <path
      d="M24 2C12 2 2 12 2 24S12 46 24 46 46 36 46 24 36 2 24 2ZM24 37.7C22.75 37.7 21.76 36.69 21.76 35.44S22.75 33.2 24 33.2 26.26 34.21 26.26 35.44 25.25 37.7 24 37.7ZM27.54 26.05A2.71 2.71 90 0025.8 28.57V29.84C25.8 30.1 25.6 30.3 25.36 30.3H22.66C22.4 30.3 22.2 30.1 22.2 29.84V28.63C22.2 27.34 22.57 26.05 23.32 24.98 24.05 23.94 25.06 23.15 26.26 22.69 28.16 21.95 29.4 20.35 29.4 18.6 29.4 16.12 26.98 14.1 24 14.1S18.6 16.12 18.6 18.6V19.02C18.6 19.27 18.4 19.48 18.16 19.48H15.46C15.2 19.48 15 19.27 15 19.02V18.6C15 16.39 15.97 14.33 17.72 12.79 19.42 11.3 21.64 10.5 24 10.5S28.6 11.32 30.28 12.79C32.03 14.33 33 16.39 33 18.6 33 21.85 30.85 24.77 27.54 26.05Z"
    />
  </svg>
</template>

<script lang="ts" setup>
  defineOptions({ name: 'QuestionCircleFilled' });
</script>
