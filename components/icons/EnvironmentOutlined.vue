<template>
  <svg
    viewBox="0 0 48 48"
    fill="none"
    stroke="currentColor"
    stroke-width="3.5"
    stroke-linejoin="round"
  >
    <path
      d="M24 46C25 46 43 33 43 21 43 10 35 2 24 2 13 2 5 10 5 21 5 33 23 46 24 46Z"
    />
    <path d="M24 28C20 28 17 25 17 21S20 14 24 14 31 17 31 21 28 28 24 28Z" />
  </svg>
</template>

<script lang="ts" setup>
  defineOptions({ name: 'EnvironmentOutlined' });
</script>
