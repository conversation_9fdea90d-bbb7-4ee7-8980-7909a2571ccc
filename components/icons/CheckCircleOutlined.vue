<template>
  <svg
    viewBox="0 0 48 48"
    fill="none"
    stroke="currentColor"
    stroke-width="4"
    stroke-linecap="round"
    stroke-linejoin="round"
  >
    <path
      d="M24 45C35 45 45 35 45 24 45 13 35 3 24 3 13 3 3 13 3 24 3 35 13 45 24 45Z"
    />
    <path d="M14 25 21 32 35 18" />
  </svg>
</template>

<script lang="ts" setup>
  defineOptions({ name: 'CheckCircleOutlined' });
</script>
