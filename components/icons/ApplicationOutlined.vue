<template>
  <svg viewBox="0 0 48 48" fill="currentColor">
    <circle cx="9" cy="9" r="4" />
    <circle cx="9" cy="24" r="4" />
    <circle cx="9" cy="39" r="4" />
    <circle cx="24" cy="9" r="4" />
    <circle cx="24" cy="24" r="4" />
    <circle cx="24" cy="39" r="4" />
    <circle cx="39" cy="9" r="4" />
    <circle cx="39" cy="24" r="4" />
    <circle cx="39" cy="39" r="4" />
  </svg>
</template>

<script lang="ts" setup>
  defineOptions({ name: 'ApplicationOutlined' });
</script>
