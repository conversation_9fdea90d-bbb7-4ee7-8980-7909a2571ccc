<template>
  <svg
    viewBox="0 0 48 48"
    fill="none"
    stroke="currentColor"
    stroke-width="4"
    stroke-linecap="round"
  >
    <path
      d="M24 35C30 35 35 30 35 24 35 18 30 13 24 13 18 13 13 18 13 24 13 30 18 35 24 35Z"
    />
    <path d="M24 2V6" />
    <path d="M8 8 11 11" />
    <path d="M2 24H6" />
    <path d="M8 40 11 37" />
    <path d="M24 46V42" />
    <path d="M40 40 37 37" />
    <path d="M46 24H42" />
    <path d="M40 8 37 11" />
  </svg>
</template>

<script lang="ts" setup>
  defineOptions({ name: 'SunOutlined' });
</script>
