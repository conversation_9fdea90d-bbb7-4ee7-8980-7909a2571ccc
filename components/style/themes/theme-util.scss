@use '../../ele-admin-layout/style/css-var.scss' as *;
@use '../../ele-alert/style/css-var.scss' as *;
@use '../../ele-backtop/style/css-var.scss' as *;
@use '../../ele-app/style/css-var.scss' as *;
@use '../../ele-basic-select/style/css-var.scss' as *;
@use '../../ele-bottom-bar/style/css-var.scss' as *;
@use '../../ele-card/style/css-var.scss' as *;
@use '../../ele-check-card/style/css-var.scss' as *;
@use '../../ele-data-table/style/css-var.scss' as *;
@use '../../ele-drawer/style/css-var.scss' as *;
@use '../../ele-dropdown/style/css-var.scss' as *;
@use '../../ele-file-list/style/css-var.scss' as *;
@use '../../ele-icon-select/style/css-var.scss' as *;
@use '../../ele-image-viewer/style/css-var.scss' as *;
@use '../../ele-loading/style/css-var.scss' as *;
@use '../../ele-map-picker/style/css-var.scss' as *;
@use '../../ele-menus/style/css-var.scss' as *;
@use '../../ele-modal/style/css-var.scss' as *;
@use '../../ele-page/style/css-var.scss' as *;
@use '../../ele-pagination/style/css-var.scss' as *;
@use '../../ele-popconfirm/style/css-var.scss' as *;
@use '../../ele-pro-form/style/css-var.scss' as *;
@use '../../ele-pro-form-builder/style/css-var.scss' as *;
@use '../../ele-pro-table/style/css-var.scss' as *;
@use '../../ele-segmented/style/css-var.scss' as *;
@use '../../ele-steps/style/css-var.scss' as *;
@use '../../ele-tab-bar/style/css-var.scss' as *;
@use '../../ele-table/style/css-var.scss' as *;
@use '../../ele-table-select/style/css-var.scss' as *;
@use '../../ele-tabs/style/css-var.scss' as *;
@use '../../ele-text/style/css-var.scss' as *;
@use '../../ele-timeline/style/css-var.scss' as *;
@use '../../ele-tool/style/css-var.scss' as *;
@use '../../ele-toolbar/style/css-var.scss' as *;
@use '../../ele-tooltip/style/css-var.scss' as *;
@use '../../ele-tree-select/style/css-var.scss' as *;
@use '../../ele-tree-table/style/css-var.scss' as *;
@use '../../ele-upload-list/style/css-var.scss' as *;
@use '../../ele-viewer/style/css-var.scss' as *;
@use '../../ele-virtual-table/style/css-var.scss' as *;

/* 生成主题的 css 变量 */
@mixin set-theme-var($selector, $var) {
  #{$selector} {
    @include set-layout-var($var);
    @include set-alert-var($var);
    @include set-backtop-var($var);
    @include set-app-var($var);
    @include set-basic-select-var($var);
    @include set-bottom-bar-var($var);
    @include set-card-var($var);
    @include set-check-card-var($var);
    @include set-data-table-var($var);
    @include set-drawer-var($var);
    @include set-dropdown-var($var);
    @include set-file-var($var);
    @include set-icon-var($var);
    @include set-image-viewer-var($var);
    @include set-loading-var($var);
    @include set-map-var($var);
    @include set-menus-var($var);
    @include set-modal-var($var);
    @include set-page-var($var);
    @include set-pagination-var($var);
    @include set-popconfirm-var($var);
    @include set-pro-form-var($var);
    @include set-pro-form-builder-var($var);
    @include set-pro-table-var($var);
    @include set-segmented-var($var);
    @include set-step-var($var);
    @include set-tab-bar-var($var);
    @include set-table-var($var);
    @include set-table-select-var($var);
    @include set-tab-var($var);
    @include set-text-var($var);
    @include set-timeline-var($var);
    @include set-tool-var($var);
    @include set-toolbar-var($var);
    @include set-tooltip-var($var);
    @include set-tree-select-var($var);
    @include set-tree-table-var($var);
    @include set-upload-list-var($var);
    @include set-viewer-var($var);
    @include set-virtual-table-var($var);
  }
}
