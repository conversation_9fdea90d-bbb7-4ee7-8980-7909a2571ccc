/** 暗黑主题 */
@use 'sass:map';
@use '../util.scss' as *;

$ele-dark: () !default;
$ele-dark: map.deep-merge(
  (
    'scrollbar': (
      'color': #484848,
      'hover-color': #5b5b5b
    ),
    'header': (
      'shadow': 0 1px 4px rgba(0, 0, 0, 0.6),
      'tool-hover-bg': rgba(255, 255, 255, 0.05),
      'dark-tool-hover-bg': rgba(255, 255, 255, 0.05),
      'dark-bg': #1f1f1f,
      'dark-shadow': 0 1px 4px rgba(0, 0, 0, 0.6),
      'ghost-bg': hsla(0, 0%, 32%, 0.4)
    ),
    'logo': (
      'shadow': 0 3px 4px rgba(0, 0, 0, 0.6),
      'dark-shadow': 0 3px 4px rgba(0, 0, 0, 0.6)
    ),
    'sidebar': (
      'dark-bg': #1f1f1f,
      'shadow': 0 4px 4px rgba(0, 0, 0, 0.6),
      'dark-shadow': 0 4px 4px rgba(0, 0, 0, 0.6)
    ),
    'menu-thumb': (
      'color': #484848,
      'hover-color': #5b5b5b
    ),
    'menu-dark-thumb': (
      'color': #626262,
      'hover-color': #5b5b5b
    ),
    'menu': (
      'child-bg': rgba(255, 255, 255, 0.04)
    ),
    'menu-dark': (
      'bg': #1f1f1f,
      'child-bg': #141414
    ),
    'menu-colorful': (
      'dot-color': #5a5a5a,
      'dot-hover-color': #8b8b8b
    ),
    'segmented': (
      'bg': elVar('bg-color', 'page'),
      'hover-bg': rgba(255, 255, 255, 0.05)
    ),
    'tab': (
      'scroll-left-shadow': inset 10px 0 10px -10px rgba(0, 0, 0, 0.88),
      'scroll-right-shadow': inset -10px 0 10px -10px rgba(0, 0, 0, 0.88),
      'simple-hover-bg': rgba(255, 255, 255, 0.05),
      'indicator-dot-color': #5a5a5a,
      'indicator-dot-hover-color': #8b8b8b,
      'button-bg': hsla(0, 0%, 100%, 0.03),
      'button-hover-bg': hsla(0, 0%, 100%, 0.04)
    ),
    'table': (
      'icon-hover-bg': rgba(255, 255, 255, 0.12),
      'fixed-left-shadow': inset 10px 0 10px -10px rgba(0, 0, 0, 0.88),
      'fixed-right-shadow': inset -10px 0 10px -10px rgba(0, 0, 0, 0.88)
    ),
    'tool': (
      'hover-bg': rgba(255, 255, 255, 0.05)
    ),
    'popper': (
      'arrow-shadow': 0 0 8px 0 rgba(0, 0, 0, 0.28)
    ),
    'tooltip': (
      'bg': '#383838',
      'shadow': 0 0 8px 0 rgba(0, 0, 0, 0.28),
      'arrow-shadow': 0 0 8px 0 rgba(0, 0, 0, 0.28)
    ),
    'tag': (
      'info-bg': elVar('color-info', 'light-8')
    )
  ),
  $ele-dark
);
