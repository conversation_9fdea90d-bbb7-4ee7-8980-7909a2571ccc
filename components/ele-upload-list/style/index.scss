@use '../../style/themes/default.scss' as *;
@use '../../style/util.scss' as *;
@use './css-var.scss' as *;

@include set-upload-list-var($ele);

.ele-upload-list {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: eleVar('upload-list', 'gap');
  box-sizing: border-box;
  max-width: 100%;
  $inner-radius: calc(#{eleVar('upload-list', 'radius')} - 1px);

  .ele-upload-item {
    flex-shrink: 0;
    width: eleVar('upload-list', 'width');
    height: eleVar('upload-list', 'height');
    padding: eleVar('upload-list', 'padding');
    border-radius: eleVar('upload-list', 'radius');
    border: eleVar('upload-list', 'border');
    box-sizing: border-box;
    position: relative;
    user-select: none;
    overflow: hidden;
    max-width: 100%;
    cursor: pointer;
  }

  /* 缩略图片 */
  .ele-upload-image {
    width: 100%;
    height: 100%;
    display: block;
    pointer-events: none;
  }

  /* 缩略图标 */
  .ele-upload-thumbnail {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: eleVar('upload-list', 'thumbnail-gap');
    color: eleVar('upload-list', 'thumbnail-color');
    font-size: eleVar('upload-list', 'thumbnail-font-size');
    text-align: center;
  }

  .ele-upload-thumbnail-icon {
    flex-shrink: 0;
    color: eleVar('upload-list', 'thumbnail-icon-color');
    font-size: eleVar('upload-list', 'thumbnail-icon-font-size');
  }

  .ele-upload-thumbnail-text {
    width: 100%;
    overflow: hidden;
    line-height: 1.15;
    word-break: break-all;
    display: -webkit-box;
    line-clamp: 3;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  /* 操作按钮 */
  .ele-upload-tools {
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom-left-radius: $inner-radius;
    border-bottom-right-radius: $inner-radius;
    background: eleVar('upload-list', 'tool-bg');
    position: absolute;
    left: 0;
    right: 0;
    bottom: -20px;
    transition: all $transition-base;
    box-sizing: border-box;
    overflow: hidden;
    opacity: 0;
    z-index: 3;
  }

  .ele-upload-tool {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    color: eleVar('upload-list', 'tool-color');
    font-size: eleVar('upload-list', 'tool-font-size');
    padding: eleVar('upload-list', 'tool-padding');
    transition: all $transition-base;
    box-sizing: border-box;
    position: relative;
    cursor: pointer;

    &:hover {
      background: eleVar('upload-list', 'tool-hover-bg');
    }

    & + .ele-upload-tool {
      margin-left: eleVar('upload-list', 'tool-gap');

      &::before {
        content: '';
        width: 0px;
        height: 56%;
        border-left: 1px solid eleVar('upload-list', 'tool-color');
        box-sizing: border-box;
        position: absolute;
        top: 0;
        bottom: 0;
        left: -0.5px;
        margin: auto 0;
        opacity: 0.6;
      }
    }
  }

  .ele-upload-item:hover .ele-upload-tools {
    bottom: 0;
    opacity: 1;
  }

  .ele-upload-tool-text {
    margin-left: 2px;
  }

  /* 删除按钮 */
  .ele-upload-remove {
    flex: none;
    padding: 0;
    width: eleVar('upload-list', 'del-size');
    height: eleVar('upload-list', 'del-size');
    color: eleVar('upload-list', 'tool-color');
    font-size: eleVar('upload-list', 'tool-font-size');
    background: eleVar('upload-list', 'tool-bg');
    box-shadow: eleVar('upload-list', 'del-shadow');
    border-top-right-radius: $inner-radius;
    border-bottom-left-radius: 100%;
    position: absolute;
    top: 0;
    right: 0;
    line-height: 1;
    transition: all $transition-base;
    box-sizing: border-box;
    cursor: pointer;
    z-index: 4;

    & > i {
      margin: eleVar('upload-list', 'del-icon-margin');
    }

    &:hover {
      background: eleVar('upload-list', 'del-hover-bg');
    }
  }

  /* 进度条 */
  .ele-upload-progress {
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: eleVar('upload-list', 'progress-color');
    font-size: eleVar('upload-list', 'progress-font-size');
    padding: eleVar('upload-list', 'progress-padding');
    background: eleVar('upload-list', 'progress-bg');
    line-height: 1;
    position: absolute;
    top: eleVar('upload-list', 'padding');
    left: eleVar('upload-list', 'padding');
    right: eleVar('upload-list', 'padding');
    bottom: eleVar('upload-list', 'padding');
    box-sizing: border-box;

    .el-progress-bar__outer {
      background: hsla(0, 0%, 80%, 0.48);
    }
  }

  .ele-upload-text {
    flex-shrink: 0;
    margin-bottom: 8px;
    text-align: center;
  }

  /* 重试按钮 */
  .ele-upload-retry {
    flex: none;
    width: max-content;
    margin: 6px auto 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: eleVar('upload-list', 'progress-tool-gap');
    font-size: eleVar('upload-list', 'progress-tool-font-size');
    border-radius: eleVar('upload-list', 'progress-tool-radius');
    padding: eleVar('upload-list', 'progress-tool-padding');
    background: eleVar('upload-list', 'progress-tool-bg');
    transition: all $transition-base;
    box-sizing: border-box;
    position: relative;
    cursor: pointer;
    z-index: 3;

    &:hover {
      background: eleVar('upload-list', 'progress-tool-hover-bg');
    }

    .ele-upload-tool-text {
      margin: 0;
    }
  }

  /* 上传按钮 */
  .ele-upload-button {
    padding: 0;
    text-align: center;
    border: eleVar('upload-list', 'btn-border');
    background: eleVar('upload-list', 'btn-bg');
    transition: all $transition-base;
    overflow: visible;
    cursor: pointer;

    &:hover {
      border: eleVar('upload-list', 'btn-hover-border');
    }

    & > div {
      height: 100%;
      display: block;
    }

    .el-upload {
      height: 100%;
      display: flex;
      cursor: inherit;
    }

    .el-upload-dragger {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: eleVar('upload-list', 'radius');
      transition: all $transition-base;
      outline: 1px dashed transparent;
      background: none;
      cursor: inherit;
      border: none;
      padding: 0;

      &.is-dragover {
        outline: eleVar('upload-list', 'btn-hover-border');
        background: eleVar('upload-list', 'btn-drag-bg');
      }
    }
  }

  .ele-upload-button-inner {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
  }

  .ele-upload-icon {
    color: eleVar('upload-list', 'btn-color');
    font-size: eleVar('upload-list', 'btn-font-size');

    & > svg {
      stroke-width: 3;
    }
  }

  /* 修改时的文件选择输入框 */
  .ele-upload-input,
  .ele-upload-hidden {
    display: none;
  }

  /* 拖动手柄 */
  .ele-upload-handle {
    cursor: move;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
  }
}

/* 禁用状态 */
.ele-upload-list.is-disabled {
  .ele-upload-button {
    background: eleVar('upload-list', 'btn-disabled-bg');
    border: eleVar('upload-list', 'btn-disabled-border');
    cursor: not-allowed;
  }

  .ele-upload-icon {
    color: eleVar('upload-list', 'btn-disabled-color');
  }
}

/* 拖动状态 */
.ele-upload-item {
  &.sortable-chosen {
    background: eleVar('upload-list', 'bg');
  }

  &.sortable-ghost {
    opacity: 0;
  }

  &.sortable-fallback {
    opacity: 1 !important;
  }
}

.ele-upload-list:has(.ele-upload-item.sortable-ghost) {
  .ele-upload-tools {
    display: none;
  }

  .ele-upload-button {
    pointer-events: none;
  }
}

/* 列表文件样式 */
.ele-upload-list.is-file-list {
  width: 100%;

  .ele-upload-item {
    width: 100%;
    height: eleVar('upload-list', 'file-height');
    padding: 0 eleVar('upload-list', 'file-padding');
    display: flex;
    align-items: center;
  }

  /* 缩略图标 */
  .ele-upload-thumbnail {
    flex: 1;
    flex-direction: row;
    justify-content: flex-start;
    font-size: eleVar('upload-list', 'file-thumbnail-font-size');
    overflow: hidden;
    text-align: left;
  }

  .ele-upload-thumbnail-icon {
    color: eleVar('upload-list', 'file-thumbnail-icon-color');
    font-size: eleVar('upload-list', 'file-thumbnail-icon-font-size');
  }

  .ele-upload-thumbnail-text {
    flex: 1;
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  /* 操作按钮 */
  .ele-upload-tools {
    background: none;
    position: relative;
    left: auto;
    right: auto;
    bottom: auto;
    opacity: 1;
  }

  .ele-upload-item:not(:hover) .ele-upload-tools {
    display: none;
  }

  .ele-upload-tool,
  .ele-upload-remove,
  .ele-upload-retry {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0 0 0 eleVar('upload-list', 'file-tool-gap');
    width: eleVar('upload-list', 'file-tool-size');
    height: eleVar('upload-list', 'file-tool-size');
    border-radius: eleVar('upload-list', 'file-tool-radius');
    color: eleVar('upload-list', 'file-tool-color');
    box-shadow: none;
    background: none;

    &:hover {
      color: eleVar('upload-list', 'file-tool-hover-color');
      background: eleVar('upload-list', 'file-tool-hover-bg');
    }
  }

  .ele-upload-tool::before {
    display: none;
  }

  .ele-upload-tool-text {
    display: none;
  }

  /* 删除按钮 */
  .ele-upload-remove {
    position: relative;
    top: auto;
    right: auto;
    margin: eleVar('upload-list', 'file-del-margin');

    & > i {
      margin: 0;
    }

    &:hover {
      color: eleVar('upload-list', 'file-del-hover-color');
    }
  }

  /* 进度条 */
  .ele-upload-progress {
    position: static;
    flex-direction: row;
    background: none;
    padding: 0;

    .el-progress-bar__outer {
      background: transparent;
    }
  }

  .ele-upload-progress-bar {
    position: absolute;
    left: eleVar('upload-list', 'file-padding');
    right: eleVar('upload-list', 'file-padding');
    bottom: 0;
  }

  .ele-upload-text {
    display: none;
  }

  /* 上传按钮 */
  .ele-upload-button {
    padding: 0;
    display: block;
  }

  .ele-upload-icon {
    font-size: eleVar('upload-list', 'file-btn-font-size');
  }
}

/* 触摸设备始终显示操作按钮 */
@media (pointer: coarse) {
  .ele-upload-list .ele-upload-item .ele-upload-tools {
    bottom: 0;
    opacity: 1;
  }

  .ele-upload-list.is-file-list .ele-upload-item .ele-upload-tools,
  .ele-upload-list.is-file-list .ele-upload-item:not(:hover) .ele-upload-tools {
    bottom: auto;
    display: flex;
  }
}
