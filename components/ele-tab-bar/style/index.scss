@use '../../style/themes/default.scss' as *;
@use '../../style/util.scss' as *;
@use './css-var.scss' as *;

@include set-tab-bar-var($ele);

.ele-tab-bar {
  display: flex;
  align-items: center;
  padding: eleVar('tab-bar', 'padding');
  box-sizing: border-box;
  position: relative;
  overflow: auto;

  &::before {
    content: '';
    border-bottom: 1px solid eleVar('tab-bar', 'line-color');
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
  }

  &.is-plain::before {
    display: none;
  }
}

.ele-tab-nav {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: auto;
}

.ele-tab-item {
  flex-shrink: 0;
  height: eleVar('tab-bar', 'height');
  line-height: eleVar('tab-bar', 'height');
  color: eleVar('tab-bar', 'color');
  font-size: eleVar('tab-bar', 'font-size');
  padding: eleVar('tab-bar', 'item-padding');
  border-radius: eleVar('tab-bar', 'item-radius');
  transition: (color $transition-base, background-color $transition-base);
  box-sizing: border-box;
  word-break: break-all;
  white-space: nowrap;
  position: relative;
  user-select: none;
  cursor: pointer;

  & + .ele-tab-item {
    margin-left: eleVar('tab-bar', 'space');
  }

  &::after {
    content: '';
    width: 100%;
    height: eleVar('tab-bar', 'active-line-size');
    position: absolute;
    left: 0;
    bottom: 0;
    opacity: 0;
    transform: scaleX(0.0001);
    background: eleVar('tab-bar', 'active-line-color');
    transition: (
      transform 0.15s cubic-bezier(0.215, 0.61, 0.355, 1),
      opacity 0.15s cubic-bezier(0.215, 0.61, 0.355, 1)
    );
  }

  &:hover {
    color: eleVar('tab-bar', 'hover-color');
    background: eleVar('tab-bar', 'hover-bg');
  }

  &:active {
    background: eleVar('tab-bar', 'active-bg');
  }

  &.is-active {
    color: eleVar('tab-bar', 'active-color');
    background: eleVar('tab-bar', 'active-bg');
    font-weight: eleVar('tab-bar', 'active-font-weight');

    &::after {
      opacity: 1;
      transform: scaleX(1);
      transition: (
        transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1),
        opacity 0.15s cubic-bezier(0.645, 0.045, 0.355, 1)
      );
    }
  }
}
