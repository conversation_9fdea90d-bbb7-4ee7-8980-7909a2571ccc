<template>
  <div
    class="ele-icon-border-color-text"
    :style="{
      flexShrink: 0,
      width: '6px',
      margin: '0 0 0 6px',
      borderTopStyle: 'solid',
      borderTopWidth: '1px',
      borderBottomStyle: 'solid',
      borderBottomWidth: '1px',
      display: 'flex',
      justifyContent: 'center'
    }"
  >
    <div
      class="ele-icon-border-color-text"
      :style="{
        height: '10px',
        borderLeftStyle: 'solid',
        borderLeftWidth: '1px'
      }"
    ></div>
  </div>
</template>

<script lang="ts" setup>
  //
</script>
